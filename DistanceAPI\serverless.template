{"AWSTemplateFormatVersion": "2010-09-09", "Transform": "AWS::Serverless-2016-10-31", "Description": "Airport Distance API - An AWS Serverless Application that calculates distances between airports.", "Parameters": {}, "Conditions": {}, "Resources": {"AspNetCoreFunction": {"Type": "AWS::Serverless::Function", "Properties": {"Handler": "DistanceAPI::DistanceAPI.LambdaEntryPoint::FunctionHandlerAsync", "Runtime": "provided.al2", "CodeUri": "", "MemorySize": 512, "Timeout": 30, "Role": null, "Policies": ["AWSLambdaFullAccess"], "Environment": {"Variables": {}}, "Events": {"ProxyResource": {"Type": "Api", "Properties": {"Path": "/{proxy+}", "Method": "ANY"}}, "RootResource": {"Type": "Api", "Properties": {"Path": "/", "Method": "ANY"}}}}}}, "Outputs": {"ApiURL": {"Description": "API endpoint URL for Prod environment", "Value": {"Fn::Sub": "https://${ServerlessRestApi}.execute-api.${AWS::Region}.amazonaws.com/Prod/"}}}}