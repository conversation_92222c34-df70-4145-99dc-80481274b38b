using System.Collections.Generic;

namespace Model
{
    public class BatchDistanceResponse
    {
        public List<DistanceResult> Results { get; set; } = new List<DistanceResult>();
        public int TotalPairs { get; set; }
        public int SuccessfulCalculations { get; set; }
        public int FailedCalculations { get; set; }
    }

    public class DistanceResult
    {
        public string From { get; set; }
        public string To { get; set; }
        public string Distance { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
    }
}
