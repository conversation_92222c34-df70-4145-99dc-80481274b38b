﻿using Business.Abstract;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace DistanceAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AirportController : ControllerBase
    {
        private IAirportService _airportService;

        public AirportController(IAirportService airportService)
        {
            _airportService = airportService;
        }

        [HttpGet("/airports")]
        public IActionResult getAirports()
        {
            return Ok(_airportService.GetAllAirports());
        }

        [HttpGet("/airport/{iataCode}")]
        public ActionResult<Airport> GetAirportByIataCode(string iataCode)
        {
            var airport = _airportService.GetAirportByIataCode(iataCode.ToUpper());

            if (airport == null)
            {
                return NotFound();
            }

            return Ok(airport);
        }

        [HttpGet("/distance/from={from}&to={to}")]
        public IActionResult CalculateDistance(string from, string to)
        {
            string distance = _airportService.calculateDistanceOfAirports(from, to);

            return Ok(distance);
        }

        [HttpPost("/distances/batch")]
        public IActionResult CalculateBatchDistances([FromBody] BatchDistanceRequest request)
        {
            if (request == null || request.AirportPairs == null || request.AirportPairs.Count == 0)
            {
                return BadRequest(new { error = "Request must contain at least one airport pair" });
            }

            // Limit batch size to prevent abuse
            if (request.AirportPairs.Count > 100)
            {
                return BadRequest(new { error = "Maximum 100 airport pairs allowed per request" });
            }

            var response = _airportService.CalculateBatchDistances(request);
            return Ok(response);
        }
    }
}
