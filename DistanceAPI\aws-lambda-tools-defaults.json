{"Information": ["This file provides default values for the deployment wizard inside Visual Studio and the AWS Lambda commands added to the .NET Core CLI.", "To learn more about the Lambda commands with the .NET Core CLI execute the following command at the command line in the project root directory.", "dotnet lambda help", "All the command line options for the Lambda command can be specified in this file."], "profile": "", "region": "us-east-1", "configuration": "Release", "function-runtime": "dotnet6", "function-memory-size": 512, "function-timeout": 30, "function-handler": "DistanceAPI::DistanceAPI.LambdaEntryPoint::FunctionHandlerAsync", "function-name": "airport-distance-api", "function-description": "Airport Distance Calculator API - calculates distances between airports using IATA codes", "package-type": "Zip", "function-role": "", "function-architecture": "x86_64", "function-subnets": "", "function-security-groups": "", "tracing-mode": "PassThrough", "environment-variables": "", "image-tag": "", "function-url-enable": false, "function-url-cors": true, "msbuild-parameters": "--self-contained false"}