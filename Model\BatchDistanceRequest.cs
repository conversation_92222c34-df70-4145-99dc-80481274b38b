using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Model
{
    public class BatchDistanceRequest
    {
        [Required]
        public List<AirportPair> AirportPairs { get; set; } = new List<AirportPair>();
    }

    public class AirportPair
    {
        [Required]
        [StringLength(3, MinimumLength = 3)]
        public string From { get; set; }

        [Required]
        [StringLength(3, MinimumLength = 3)]
        public string To { get; set; }
    }
}
