﻿using Business.Abstract;
using DAL.Abstract;
using Helper.Abstract;
using Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class AirportService : IAirportService
    {

        private readonly IAirportRepository _airportRepository;
        private ICalculation _calculation;

        public AirportService(IAirportRepository airportRepository, ICalculation calculation)
        {
            _airportRepository = airportRepository;
            _calculation = calculation;
        }

        public string calculateDistanceOfAirports(string from, string to)
        {
            return _calculation.calculateDistance(from, to);
        }

        public Airport GetAirportByIataCode(string iataCode)
        {
            return _airportRepository.GetLatLongByIataCode(iataCode);
        }

        public IEnumerable<Airport> GetAllAirports()
        {
            return _airportRepository.getAllAirports();
        }

        public BatchDistanceResponse CalculateBatchDistances(BatchDistanceRequest request)
        {
            var response = new BatchDistanceResponse
            {
                TotalPairs = request.AirportPairs.Count
            };

            foreach (var pair in request.AirportPairs)
            {
                try
                {
                    var distance = _calculation.calculateDistance(pair.From.ToUpper(), pair.To.ToUpper());

                    response.Results.Add(new DistanceResult
                    {
                        From = pair.From.ToUpper(),
                        To = pair.To.ToUpper(),
                        Distance = distance,
                        Success = !distance.Contains("Bulunamadı") && !distance.Contains("not found"), // Handle both Turkish and English error messages
                        ErrorMessage = distance.Contains("Bulunamadı") || distance.Contains("not found") ? distance : null
                    });

                    if (!distance.Contains("Bulunamadı") && !distance.Contains("not found"))
                    {
                        response.SuccessfulCalculations++;
                    }
                    else
                    {
                        response.FailedCalculations++;
                    }
                }
                catch (Exception ex)
                {
                    response.Results.Add(new DistanceResult
                    {
                        From = pair.From.ToUpper(),
                        To = pair.To.ToUpper(),
                        Distance = null,
                        Success = false,
                        ErrorMessage = $"Error calculating distance: {ex.Message}"
                    });
                    response.FailedCalculations++;
                }
            }

            return response;
        }
    }
}
